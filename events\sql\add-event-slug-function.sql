-- ============================================================================
-- ADD EVENT SLUG GENERATION FUNCTION
-- ============================================================================
-- This script adds a missing slug generation function for events
-- Similar to the ones used for blogs and cities
-- ============================================================================

-- Function to generate unique event slug
CREATE OR REPLACE FUNCTION generate_unique_event_slug(title_text TEXT, event_id UUID DEFAULT NULL)
RETURNS TEXT AS $$
DECLARE
    base_slug TEXT;
    final_slug TEXT;
    counter INTEGER := 0;
BEGIN
    -- Generate base slug from title
    base_slug := lower(trim(regexp_replace(title_text, '[^a-zA-Z0-9\s]', '', 'g')));
    base_slug := regexp_replace(base_slug, '\s+', '-', 'g');
    base_slug := trim(base_slug, '-');
    
    -- Ensure slug is not empty
    IF base_slug = '' THEN
        base_slug := 'event';
    END IF;
    
    final_slug := base_slug;
    
    -- Check for uniqueness and increment if needed
    WHILE EXISTS (
        SELECT 1 FROM events 
        WHERE slug = final_slug 
        AND (event_id IS NULL OR id != event_id)
    ) LOOP
        counter := counter + 1;
        final_slug := base_slug || '-' || counter;
    END LOOP;
    
    RETURN final_slug;
END;
$$ LANGUAGE plpgsql;

-- ============================================================================
-- FUNCTION CREATED SUCCESSFULLY
-- ============================================================================
-- This function can now be used in the API routes for automatic slug generation
-- Usage: SELECT generate_unique_event_slug('My Event Title');
-- ============================================================================
