# Events System Troubleshooting Guide

## Issue: "Event not found" Error

If you're seeing an error like:
```
Event not found: {
  slug: 'paper-arabia-exhibition',
  error: { code: 'PGRST116', details: 'The result contains 0 rows' }
}
```

This means the event with that slug doesn't exist in your database.

## Quick Fix Steps

### 1. Check Database Setup

First, verify your database is properly set up:

1. Open Supabase SQL Editor
2. Run the verification script:
   ```sql
   -- Copy and paste content from: events/sql/verify-setup.sql
   ```

### 2. Set Up Events Database (if missing)

If tables are missing, run the complete schema:

1. **Option A: Complete Schema (Recommended)**
   ```sql
   -- Copy and paste content from: events/sql/events-schema.sql
   ```

2. **Option B: Enhanced Schema**
   ```sql
   -- Copy and paste content from: events/sql/complete-events-schema.sql
   ```

### 3. Check Available Events

Use the debug API to see what events exist:
```
GET /api/events/debug
```

Or check in Supabase dashboard:
- Go to Table Editor
- Open `events` table
- Check what events exist and their slugs

### 4. Create Your First Event

If no events exist:

1. Go to `/admin/pages/events`
2. Click "Create Event"
3. Fill in the form and publish

## Common Issues & Solutions

### Issue: No Events in Database
**Solution:** Run the database schema or create events through admin interface

### Issue: Event Exists but Not Published
**Solution:** 
- Check `is_active = true`
- Check `published_at` is not null
- Update through admin interface

### Issue: Wrong Slug
**Solution:** 
- Check the actual slug in database
- Update URL to match existing slug
- Or update event slug in admin

### Issue: Database Connection Problems
**Solution:**
- Check Supabase connection
- Verify environment variables
- Check RLS policies

## Available Sample Events

After running the schema, you should have:
- **Slug:** `concept-big-brands-carnival-cbbc`
- **Title:** "Concept Big Brands Carnival - CBBC"

Try accessing: `/whats-on/concept-big-brands-carnival-cbbc`

## Debug Tools

1. **Debug API:** `/api/events/debug` - Shows all events and database status
2. **Verification Script:** `events/sql/verify-setup.sql` - Checks database setup
3. **Admin Interface:** `/admin/pages/events` - Manage events

## Need Help?

If you're still having issues:
1. Run the verification script
2. Check the debug API response
3. Ensure database schema is properly executed
4. Create events through the admin interface
