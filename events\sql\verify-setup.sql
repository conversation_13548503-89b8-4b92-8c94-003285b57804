-- ============================================================================
-- EVENTS SYSTEM SETUP VERIFICATION SCRIPT
-- ============================================================================
-- Run this script to verify that your events system is properly set up
-- This will help diagnose issues with missing events or database problems
-- ============================================================================

-- Check if all required tables exist
SELECT 
    'Tables Check' as check_type,
    CASE 
        WHEN COUNT(*) = 6 THEN 'PASS ✅'
        ELSE 'FAIL ❌ - Missing tables'
    END as status,
    COUNT(*) as found_tables,
    6 as expected_tables
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('event_categories', 'events', 'event_images', 'events_hero', 'event_form_submissions', 'admin_users');

-- Check if event categories exist
SELECT 
    'Event Categories' as check_type,
    CASE 
        WHEN COUNT(*) > 0 THEN 'PASS ✅'
        ELSE 'FAIL ❌ - No categories found'
    END as status,
    COUNT(*) as total_categories,
    COUNT(CASE WHEN is_active = true THEN 1 END) as active_categories
FROM event_categories;

-- Check if events exist
SELECT 
    'Events' as check_type,
    CASE 
        WHEN COUNT(*) > 0 THEN 'PASS ✅'
        ELSE 'FAIL ❌ - No events found'
    END as status,
    COUNT(*) as total_events,
    COUNT(CASE WHEN is_active = true THEN 1 END) as active_events,
    COUNT(CASE WHEN published_at IS NOT NULL THEN 1 END) as published_events
FROM events;

-- Check if storage buckets exist
SELECT 
    'Storage Buckets' as check_type,
    CASE 
        WHEN COUNT(*) >= 4 THEN 'PASS ✅'
        ELSE 'FAIL ❌ - Missing buckets'
    END as status,
    COUNT(*) as found_buckets,
    string_agg(name, ', ') as bucket_names
FROM storage.buckets 
WHERE name LIKE '%event%';

-- List all available events with their status
SELECT 
    'Available Events' as info_type,
    title,
    slug,
    is_active,
    published_at IS NOT NULL as is_published,
    created_at
FROM events 
ORDER BY created_at DESC
LIMIT 10;

-- Check for sample data
SELECT 
    'Sample Data Check' as check_type,
    CASE 
        WHEN EXISTS (SELECT 1 FROM events WHERE slug = 'concept-big-brands-carnival-cbbc') THEN 'PASS ✅'
        ELSE 'FAIL ❌ - Sample event not found'
    END as status,
    'concept-big-brands-carnival-cbbc' as expected_slug;

-- ============================================================================
-- TROUBLESHOOTING GUIDE
-- ============================================================================
/*
If any checks fail, here's what to do:

1. TABLES MISSING:
   - Run the main schema: events/sql/events-schema.sql
   - Or run: events/sql/complete-events-schema.sql

2. NO CATEGORIES:
   - Categories should be created automatically with the schema
   - Check if the INSERT statements ran successfully

3. NO EVENTS:
   - This is normal for a fresh installation
   - Create events through the admin interface at /admin/pages/events
   - Or the sample event should exist if schema was run completely

4. STORAGE BUCKETS MISSING:
   - Run the storage setup: events/sql/events-storage-setup.sql
   - Or check Supabase Storage dashboard

5. SAMPLE EVENT MISSING:
   - The schema includes a sample event 'concept-big-brands-carnival-cbbc'
   - If missing, the INSERT statements may have failed
   - Check for foreign key constraint errors (categories must exist first)
*/
