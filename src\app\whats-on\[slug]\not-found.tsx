import React from 'react';
import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { ArrowLeft, Calendar, Search } from 'lucide-react';

export default function EventNotFound() {
    return (
        <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4">
            <div className="max-w-md w-full text-center">
                <div className="mb-8">
                    <div className="mx-auto w-24 h-24 bg-gray-200 rounded-full flex items-center justify-center mb-6">
                        <Calendar className="w-12 h-12 text-gray-400" />
                    </div>
                    <h1 className="text-3xl font-bold text-gray-900 mb-4">
                        Event Not Found
                    </h1>
                    <p className="text-gray-600 mb-6">
                        The event you're looking for doesn't exist or may have been removed.
                    </p>
                </div>

                <div className="space-y-4">
                    <Link href="/whats-on">
                        <Button className="w-full bg-[#a5cd39] hover:bg-[#8fb82e] text-white">
                            <Calendar className="w-4 h-4 mr-2" />
                            View All Events
                        </Button>
                    </Link>
                    
                    <Link href="/">
                        <Button variant="outline" className="w-full">
                            <ArrowLeft className="w-4 h-4 mr-2" />
                            Back to Home
                        </Button>
                    </Link>
                </div>

                <div className="mt-8 p-4 bg-blue-50 rounded-lg">
                    <h3 className="text-sm font-semibold text-blue-900 mb-2">
                        Looking for something specific?
                    </h3>
                    <p className="text-sm text-blue-700">
                        Try browsing our events page or contact us if you need help finding a particular event.
                    </p>
                </div>
            </div>
        </div>
    );
}
