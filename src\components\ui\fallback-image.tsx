"use client";

import React, { useState, useCallback } from 'react';
import Image from 'next/image';
import { ImageIcon } from 'lucide-react';

interface FallbackImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
  fill?: boolean;
  sizes?: string;
  priority?: boolean;
  quality?: number;
  fallbackSrc?: string;
  showPlaceholder?: boolean;
}

const FallbackImage: React.FC<FallbackImageProps> = ({
  src,
  alt,
  width,
  height,
  className = '',
  fill = false,
  sizes,
  priority = false,
  quality = 75,
  fallbackSrc,
  showPlaceholder = true,
}) => {
  const [imageError, setImageError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  const handleError = useCallback(() => {
    setImageError(true);
    setIsLoading(false);
  }, []);

  const handleLoad = useCallback(() => {
    setIsLoading(false);
  }, []);

  // Default fallback image
  const defaultFallback = '/images/placeholder.svg';
  const finalFallbackSrc = fallbackSrc || defaultFallback;

  // If there's an error and no fallback, show placeholder
  if (imageError && !fallbackSrc && showPlaceholder) {
    return (
      <div 
        className={`bg-gray-200 flex items-center justify-center ${className}`}
        style={fill ? {} : { width, height }}
      >
        <ImageIcon className="w-8 h-8 text-gray-400" />
        <span className="sr-only">{alt}</span>
      </div>
    );
  }

  // Determine which source to use
  const imageSrc = imageError ? finalFallbackSrc : src;

  return (
    <div className={`relative ${className}`}>
      {isLoading && showPlaceholder && (
        <div 
          className="absolute inset-0 bg-gray-200 animate-pulse flex items-center justify-center"
          style={fill ? {} : { width, height }}
        >
          <ImageIcon className="w-8 h-8 text-gray-400" />
        </div>
      )}
      <Image
        src={imageSrc}
        alt={alt}
        width={fill ? undefined : width}
        height={fill ? undefined : height}
        fill={fill}
        sizes={sizes}
        priority={priority}
        quality={quality}
        className={`${isLoading ? 'opacity-0' : 'opacity-100'} transition-opacity duration-300`}
        onError={handleError}
        onLoad={handleLoad}
        // Add timeout handling
        style={{
          objectFit: 'cover',
        }}
      />
    </div>
  );
};

export default FallbackImage;
