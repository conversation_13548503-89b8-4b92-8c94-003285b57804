import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/lib/supabase/server";

// GET /api/events/debug - Debug endpoint to check what events exist
export async function GET(request: NextRequest) {
    try {
        const supabase = await createClient();

        // Get all events regardless of status
        const { data: allEvents, error: allEventsError } = await supabase
            .from('events')
            .select('id, title, slug, is_active, published_at, created_at')
            .order('created_at', { ascending: false });

        // Get published events only
        const { data: publishedEvents, error: publishedError } = await supabase
            .from('events')
            .select('id, title, slug, is_active, published_at')
            .eq('is_active', true)
            .not('published_at', 'is', null)
            .order('created_at', { ascending: false });

        // Get event categories
        const { data: categories, error: categoriesError } = await supabase
            .from('event_categories')
            .select('id, name, slug, is_active')
            .order('name');

        // Check if tables exist by trying to get table info
        const { data: tableInfo, error: tableError } = await supabase
            .from('information_schema.tables')
            .select('table_name')
            .eq('table_schema', 'public')
            .like('table_name', 'event%');

        return NextResponse.json({
            success: true,
            debug: {
                allEvents: {
                    count: allEvents?.length || 0,
                    data: allEvents || [],
                    error: allEventsError
                },
                publishedEvents: {
                    count: publishedEvents?.length || 0,
                    data: publishedEvents || [],
                    error: publishedError
                },
                categories: {
                    count: categories?.length || 0,
                    data: categories || [],
                    error: categoriesError
                },
                tables: {
                    data: tableInfo || [],
                    error: tableError
                }
            }
        });

    } catch (error) {
        console.error('Debug API Error:', error);
        return NextResponse.json(
            { 
                success: false,
                error: 'Internal server error',
                details: error instanceof Error ? error.message : 'Unknown error'
            },
            { status: 500 }
        );
    }
}
